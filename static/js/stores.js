// Store management functionality - OAuth Only

let stores = [];

// Initialize stores page
function initStoresPage() {
    // Require authentication
    if (!requireAuth()) return;

    // Load stores
    loadStores();

    // Setup event listeners
    setupEventListeners();

    // Check for OAuth callback results
    checkOAuthCallback();
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Add store button
    const addStoreBtn = document.getElementById('add-store-btn');
    if (addStoreBtn) {
        addStoreBtn.addEventListener('click', showOAuthInfo);
        console.log('Add store button listener attached');
    }

    // OAuth buttons
    setupOAuthEventListeners();
}

// Setup OAuth event listeners
function setupOAuthEventListeners() {
    const startOAuthBtn = document.getElementById('start-oauth-btn');
    if (startOAuthBtn) {
        startOAuthBtn.removeEventListener('click', startOAuthFlow);
        startOAuthBtn.addEventListener('click', startOAuthFlow);
        console.log('Start OAuth button listener attached');
    }

    const cancelOAuthBtn = document.getElementById('cancel-oauth-btn');
    if (cancelOAuthBtn) {
        cancelOAuthBtn.removeEventListener('click', hideOAuthInfo);
        cancelOAuthBtn.addEventListener('click', hideOAuthInfo);
        console.log('Cancel OAuth button listener attached');
    }
}

// Check for OAuth callback results
function checkOAuthCallback() {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.has('oauth')) {
        const oauthStatus = urlParams.get('oauth');
        
        if (oauthStatus === 'success') {
            const storeId = urlParams.get('store_id');
            const shop = urlParams.get('shop');
            showSuccess(`Store ${shop} connected successfully!`);
            // Reload stores to show the new one
            setTimeout(loadStores, 1000);
        } else if (oauthStatus === 'error') {
            const message = urlParams.get('message') || 'OAuth connection failed';
            showError(`OAuth Error: ${message}`);
        }

        // Clean up URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
    }
}

// Show OAuth connection info
function showOAuthInfo() {
    document.getElementById('oauth-info').style.display = 'block';
    hideMessages();
}

// Hide OAuth connection info
function hideOAuthInfo() {
    document.getElementById('oauth-info').style.display = 'none';
    hideMessages();
}

// Start OAuth flow
async function startOAuthFlow() {
    const shop = prompt('Enter your Shopify store domain (e.g., mystore.myshopify.com):');
    
    if (!shop) {
        return;
    }

    // Validate shop domain format
    const shopDomain = shop.trim().toLowerCase();
    if (!shopDomain.endsWith('.myshopify.com')) {
        showError('Please enter a valid Shopify domain (e.g., mystore.myshopify.com)');
        return;
    }

    // Show loading state
    const loading = document.getElementById('oauth-loading');
    const startBtn = document.getElementById('start-oauth-btn');
    
    loading.style.display = 'block';
    startBtn.disabled = true;
    hideMessages();

    try {
        // Redirect to OAuth installation endpoint
        const oauthUrl = `/stores/oauth/install?shop=${encodeURIComponent(shopDomain)}`;
        window.location.href = oauthUrl;
    } catch (error) {
        showError('Failed to start OAuth flow');
        loading.style.display = 'none';
        startBtn.disabled = false;
    }
}

// Load stores from API
async function loadStores() {
    try {
        const response = await fetch('/stores/?include_stats=true', {
            headers: getAuthHeaders()
        });

        if (response.ok) {
            stores = await response.json();
            renderStores();
        } else {
            showError('Failed to load stores');
        }
    } catch (error) {
        showError('Network error while loading stores');
    }
}

// Render stores in the grid
function renderStores() {
    const storesGrid = document.getElementById('stores-grid');
    const emptyState = document.getElementById('empty-state');

    if (stores.length === 0) {
        storesGrid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    storesGrid.style.display = 'grid';
    emptyState.style.display = 'none';

    storesGrid.innerHTML = stores.map(store => createStoreCard(store)).join('');
}

// Create store card HTML
function createStoreCard(store) {
    const stats = store.stats || {};
    const statusClass = `status-${store.status}`;

    return `
        <div class="store-card" data-store-id="${store.id}">
            <div class="store-header">
                <div class="store-name">${store.store_name || store.shop_url}</div>
                <div class="store-url">${store.shop_url}</div>
                <span class="store-status ${statusClass}">${store.status}</span>
            </div>

            <div class="store-stats">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_products || 0}</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_orders || 0}</div>
                        <div class="stat-label">Orders</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_customers || 0}</div>
                        <div class="stat-label">Customers</div>
                    </div>
                </div>
            </div>

            <div class="store-actions">
                <button onclick="syncStore(${store.id})" class="btn btn-primary btn-sm">
                    Sync
                </button>
                <button onclick="deleteStore(${store.id})" class="btn btn-danger btn-sm">
                    Delete
                </button>
            </div>
        </div>
    `;
}

// Sync store data
async function syncStore(storeId, forceSync = false) {
    let confirmMessage = 'Sync store data from Shopify?';
    if (forceSync) {
        confirmMessage = 'Force sync store data from Shopify? This will refresh all data even if no changes are detected.';
    }

    if (!confirm(confirmMessage)) return;

    try {
        const requestBody = forceSync ? { force_sync: true } : {};

        const response = await fetch(`/stores/${storeId}/sync`, {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message || 'Store sync completed successfully');
            loadStores(); // Reload stores to show updated data
        } else {
            const error = await response.json();
            showError(error.detail || 'Store sync failed');
        }
    } catch (error) {
        showError('Network error during store sync');
    }
}

// Force sync store data
async function forceSyncStore(storeId) {
    await syncStore(storeId, true);
}

// Delete store
async function deleteStore(storeId) {
    const store = stores.find(s => s.id === storeId);
    const storeName = store ? (store.store_name || store.shop_url) : 'this store';

    if (!confirm(`Are you sure you want to delete ${storeName}? This action cannot be undone.`)) {
        return;
    }

    try {
        const response = await fetch(`/stores/${storeId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            showSuccess('Store deleted successfully');
            loadStores(); // Reload stores
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to delete store');
        }
    } catch (error) {
        showError('Network error while deleting store');
    }
}

// Show error message
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';

    // Hide success message
    document.getElementById('success-message').style.display = 'none';

    // Auto-hide after 5 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('success-message');
    successDiv.textContent = message;
    successDiv.style.display = 'block';

    // Hide error message
    document.getElementById('error-message').style.display = 'none';

    // Auto-hide after 3 seconds
    setTimeout(() => {
        successDiv.style.display = 'none';
    }, 3000);
}

// Hide all messages
function hideMessages() {
    document.getElementById('error-message').style.display = 'none';
    document.getElementById('success-message').style.display = 'none';
}

// Make functions available globally for HTML onclick handlers
window.syncStore = syncStore;
window.forceSyncStore = forceSyncStore;
window.deleteStore = deleteStore;
window.showOAuthInfo = showOAuthInfo;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname === '/stores') {
        initStoresPage();
    }
});
