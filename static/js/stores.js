// Store management functionality - Professional OAuth Interface

let stores = [];
let isConnecting = false;

// Initialize stores page
function initStoresPage() {
    // Require authentication
    if (!requireAuth()) return;

    // Load stores
    loadStores();

    // Setup event listeners
    setupEventListeners();

    // Check for OAuth callback results
    checkOAuthCallback();
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Add store button
    const addStoreBtn = document.getElementById('add-store-btn');
    if (addStoreBtn) {
        addStoreBtn.addEventListener('click', showStoreConnectionModal);
        console.log('Add store button listener attached');
    }

    // Modal event listeners
    setupModalEventListeners();
}

// Setup modal event listeners
function setupModalEventListeners() {
    // Close modal button
    const closeModalBtn = document.getElementById('close-modal-btn');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', hideStoreConnectionModal);
    }

    // Cancel connection button
    const cancelConnectionBtn = document.getElementById('cancel-connection-btn');
    if (cancelConnectionBtn) {
        cancelConnectionBtn.addEventListener('click', hideStoreConnectionModal);
    }

    // Store connection form
    const connectionForm = document.getElementById('store-connection-form');
    if (connectionForm) {
        connectionForm.addEventListener('submit', handleStoreConnection);
    }

    // Shop domain input validation
    const shopDomainInput = document.getElementById('shop-domain');
    if (shopDomainInput) {
        shopDomainInput.addEventListener('input', validateShopDomain);
        shopDomainInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const connectBtn = document.getElementById('connect-store-btn');
                if (!connectBtn.disabled) {
                    handleStoreConnection(e);
                }
            }
        });
    }

    // Close modal when clicking outside
    const modalOverlay = document.getElementById('store-connection-modal');
    if (modalOverlay) {
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                hideStoreConnectionModal();
            }
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('store-connection-modal');
            if (modal && modal.style.display !== 'none') {
                hideStoreConnectionModal();
            }
        }
    });
}

// Check for OAuth callback results
function checkOAuthCallback() {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.has('oauth')) {
        const oauthStatus = urlParams.get('oauth');

        if (oauthStatus === 'success') {
            const storeId = urlParams.get('store_id');
            const shop = urlParams.get('shop');
            showSuccess(`Store ${shop} connected successfully! Setting up your AI assistant...`);

            // Show post-installation progress
            showPostInstallationProgress(storeId, shop);

            // Reload stores to show the new one
            setTimeout(loadStores, 2000);
        } else if (oauthStatus === 'error') {
            const message = urlParams.get('message') || 'OAuth connection failed';
            showError(`OAuth Error: ${message}`);
        }

        // Clean up URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
    }
}

// Show store connection modal
function showStoreConnectionModal() {
    const modal = document.getElementById('store-connection-modal');
    if (modal) {
        modal.style.display = 'flex';

        // Reset form state
        resetModalForm();

        // Focus on input
        setTimeout(() => {
            const input = document.getElementById('shop-domain');
            if (input) input.focus();
        }, 100);

        hideMessages();
    }
}

// Hide store connection modal
function hideStoreConnectionModal() {
    const modal = document.getElementById('store-connection-modal');
    if (modal) {
        modal.style.display = 'none';
        resetModalForm();
    }
}

// Reset modal form to initial state
function resetModalForm() {
    // Reset form
    const form = document.getElementById('store-connection-form');
    if (form) form.reset();

    // Reset validation
    const validation = document.getElementById('domain-validation');
    if (validation) {
        validation.textContent = '';
        validation.className = 'validation-message';
    }

    // Reset button state
    const connectBtn = document.getElementById('connect-store-btn');
    if (connectBtn) {
        connectBtn.disabled = true;
        const btnText = connectBtn.querySelector('.btn-text');
        const btnSpinner = connectBtn.querySelector('.btn-spinner');
        if (btnText) btnText.style.display = 'inline';
        if (btnSpinner) btnSpinner.style.display = 'none';
    }

    // Hide progress
    const progress = document.getElementById('connection-progress');
    if (progress) progress.style.display = 'none';

    // Reset steps
    updateConnectionStep(1);

    // Reset connection state
    isConnecting = false;
}

// Validate shop domain input
function validateShopDomain() {
    const input = document.getElementById('shop-domain');
    const validation = document.getElementById('domain-validation');
    const connectBtn = document.getElementById('connect-store-btn');

    if (!input || !validation || !connectBtn) return;

    const value = input.value.trim().toLowerCase();

    if (!value) {
        validation.textContent = '';
        validation.className = 'validation-message';
        connectBtn.disabled = true;
        return;
    }

    // Validate domain format
    const domainRegex = /^[a-z0-9][a-z0-9\-]*[a-z0-9]$/;

    if (value.length < 3) {
        validation.textContent = 'Store name must be at least 3 characters';
        validation.className = 'validation-message error';
        connectBtn.disabled = true;
    } else if (value.length > 60) {
        validation.textContent = 'Store name must be less than 60 characters';
        validation.className = 'validation-message error';
        connectBtn.disabled = true;
    } else if (!domainRegex.test(value)) {
        validation.textContent = 'Store name can only contain letters, numbers, and hyphens';
        validation.className = 'validation-message error';
        connectBtn.disabled = true;
    } else if (value.startsWith('-') || value.endsWith('-')) {
        validation.textContent = 'Store name cannot start or end with a hyphen';
        validation.className = 'validation-message error';
        connectBtn.disabled = true;
    } else {
        validation.textContent = `✓ Valid domain: ${value}.myshopify.com`;
        validation.className = 'validation-message success';
        connectBtn.disabled = false;
    }
}

// Handle store connection form submission
async function handleStoreConnection(e) {
    e.preventDefault();

    if (isConnecting) return;

    const input = document.getElementById('shop-domain');
    const connectBtn = document.getElementById('connect-store-btn');
    const progress = document.getElementById('connection-progress');
    const progressFill = progress?.querySelector('.progress-fill');
    const progressText = progress?.querySelector('.progress-text');

    if (!input || connectBtn.disabled) return;

    const shopName = input.value.trim().toLowerCase();
    const fullDomain = `${shopName}.myshopify.com`;

    // Set connecting state
    isConnecting = true;
    connectBtn.disabled = true;

    // Show button loading state
    const btnText = connectBtn.querySelector('.btn-text');
    const btnSpinner = connectBtn.querySelector('.btn-spinner');
    if (btnText) btnText.style.display = 'none';
    if (btnSpinner) btnSpinner.style.display = 'inline';

    // Show progress
    if (progress) progress.style.display = 'block';
    if (progressFill) progressFill.style.width = '30%';
    if (progressText) progressText.textContent = 'Validating store domain...';

    // Update to step 2
    updateConnectionStep(2);

    try {
        // Simulate validation delay for better UX
        await new Promise(resolve => setTimeout(resolve, 800));

        // Update progress
        if (progressFill) progressFill.style.width = '60%';
        if (progressText) progressText.textContent = 'Redirecting to Shopify...';

        // Another small delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update progress
        if (progressFill) progressFill.style.width = '90%';
        if (progressText) progressText.textContent = 'Initiating OAuth flow...';

        // Final delay before redirect
        await new Promise(resolve => setTimeout(resolve, 300));

        // Redirect to OAuth installation endpoint
        const oauthUrl = `/stores/oauth/install?shop=${encodeURIComponent(fullDomain)}`;
        window.location.href = oauthUrl;

    } catch (error) {
        console.error('Connection error:', error);
        showError('Failed to start connection process. Please try again.');

        // Reset state
        resetModalForm();
    }
}

// Update connection step visual indicator
function updateConnectionStep(stepNumber) {
    // Reset all steps
    for (let i = 1; i <= 3; i++) {
        const step = document.getElementById(`step-${i}`);
        if (step) {
            step.classList.remove('active');
        }
    }

    // Activate current step
    const currentStep = document.getElementById(`step-${stepNumber}`);
    if (currentStep) {
        currentStep.classList.add('active');
    }
}

// Show post-installation progress with real-time status polling
function showPostInstallationProgress(storeId, shopDomain) {
    // Create a temporary progress notification
    const progressHtml = `
        <div id="post-install-progress" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 20px;
            max-width: 350px;
            z-index: 1001;
            border-left: 4px solid #667eea;
        ">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                <div id="setup-icon" style="
                    width: 32px;
                    height: 32px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 16px;
                ">⚡</div>
                <div>
                    <h4 style="margin: 0; font-size: 14px; color: #1f2937;">Setting up ${shopDomain}</h4>
                    <p id="setup-subtitle" style="margin: 0; font-size: 12px; color: #6b7280;">Configuring your AI assistant...</p>
                </div>
            </div>
            <div style="
                width: 100%;
                height: 4px;
                background: #e5e7eb;
                border-radius: 2px;
                overflow: hidden;
            ">
                <div id="setup-progress-bar" style="
                    height: 100%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    width: 0%;
                    transition: width 0.5s ease;
                    animation: progressPulse 2s infinite;
                "></div>
            </div>
            <p id="setup-progress-text" style="
                margin: 8px 0 0 0;
                font-size: 12px;
                color: #6b7280;
            ">Initializing store data sync...</p>
        </div>
    `;

    // Add to page
    document.body.insertAdjacentHTML('beforeend', progressHtml);

    // Start real-time status polling
    pollSetupStatus(storeId, shopDomain);
}

// Poll setup status and update progress in real-time
async function pollSetupStatus(storeId, shopDomain) {
    const progressBar = document.getElementById('setup-progress-bar');
    const progressText = document.getElementById('setup-progress-text');
    const setupIcon = document.getElementById('setup-icon');
    const setupSubtitle = document.getElementById('setup-subtitle');

    let pollCount = 0;
    const maxPolls = 60; // Poll for up to 5 minutes (5s intervals)

    const pollInterval = setInterval(async () => {
        pollCount++;

        try {
            const response = await fetch(`/stores/${storeId}/setup-status`, {
                headers: getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error('Failed to fetch setup status');
            }

            const status = await response.json();

            // Update progress based on status
            updateSetupProgress(status, progressBar, progressText, setupIcon, setupSubtitle);

            // Check if setup is complete
            if (status.store_status === 'active' && status.assistant_exists && status.assistant_status === 'active') {
                // Setup complete
                clearInterval(pollInterval);
                completeSetupProgress(shopDomain);
                return;
            }

            // Check for errors
            if (status.store_status === 'error' || status.assistant_status === 'error') {
                clearInterval(pollInterval);
                errorSetupProgress(status.error || 'Setup failed');
                return;
            }

            // Stop polling after max attempts
            if (pollCount >= maxPolls) {
                clearInterval(pollInterval);
                timeoutSetupProgress();
                return;
            }

        } catch (error) {
            console.error('Setup status polling error:', error);

            // Continue polling unless we've exceeded max attempts
            if (pollCount >= maxPolls) {
                clearInterval(pollInterval);
                errorSetupProgress('Failed to check setup status');
            }
        }
    }, 5000); // Poll every 5 seconds

    // Initial progress animation
    setTimeout(() => {
        if (progressBar) progressBar.style.width = '20%';
    }, 100);
}

// Update setup progress based on status
function updateSetupProgress(status, progressBar, progressText, setupIcon, setupSubtitle) {
    if (!progressBar || !progressText) return;

    let progress = 20; // Base progress
    let message = 'Initializing...';

    // Determine progress based on status
    if (status.store_status === 'syncing') {
        progress = 40;
        message = 'Syncing store data from Shopify...';
    } else if (status.store_status === 'active') {
        progress = 70;
        message = 'Store data synced successfully';

        if (status.assistant_exists) {
            if (status.assistant_status === 'active') {
                progress = 100;
                message = 'AI assistant setup complete!';
            } else {
                progress = 85;
                message = 'Setting up AI assistant...';
            }
        } else {
            progress = 80;
            message = 'Creating AI assistant...';
        }
    }

    // Update UI
    progressBar.style.width = `${progress}%`;
    progressText.textContent = message;

    if (setupSubtitle) {
        setupSubtitle.textContent = `Store: ${status.store_name || 'Unknown'}`;
    }
}

// Complete setup progress
function completeSetupProgress(shopDomain) {
    const progressBar = document.getElementById('setup-progress-bar');
    const progressText = document.getElementById('setup-progress-text');
    const setupIcon = document.getElementById('setup-icon');
    const setupSubtitle = document.getElementById('setup-subtitle');

    if (progressBar) progressBar.style.width = '100%';
    if (progressText) progressText.textContent = 'Setup complete! Your AI assistant is ready.';
    if (setupIcon) setupIcon.textContent = '✅';
    if (setupSubtitle) setupSubtitle.textContent = 'Ready to assist customers';

    // Remove progress notification after delay
    setTimeout(() => {
        const progressElement = document.getElementById('post-install-progress');
        if (progressElement) {
            progressElement.remove();
        }
        // Reload stores to show updated status
        loadStores();
    }, 3000);
}

// Handle setup error
function errorSetupProgress(errorMessage) {
    const progressBar = document.getElementById('setup-progress-bar');
    const progressText = document.getElementById('setup-progress-text');
    const setupIcon = document.getElementById('setup-icon');
    const setupSubtitle = document.getElementById('setup-subtitle');

    if (progressBar) {
        progressBar.style.background = '#dc2626';
        progressBar.style.animation = 'none';
    }
    if (progressText) progressText.textContent = `Setup failed: ${errorMessage}`;
    if (setupIcon) {
        setupIcon.textContent = '❌';
        setupIcon.style.background = '#dc2626';
    }
    if (setupSubtitle) setupSubtitle.textContent = 'Please try reconnecting';

    // Remove progress notification after delay
    setTimeout(() => {
        const progressElement = document.getElementById('post-install-progress');
        if (progressElement) {
            progressElement.remove();
        }
        // Reload stores to show updated status
        loadStores();
    }, 5000);
}

// Handle setup timeout
function timeoutSetupProgress() {
    const progressText = document.getElementById('setup-progress-text');
    const setupSubtitle = document.getElementById('setup-subtitle');

    if (progressText) progressText.textContent = 'Setup is taking longer than expected...';
    if (setupSubtitle) setupSubtitle.textContent = 'Please check back in a few minutes';

    // Remove progress notification after delay
    setTimeout(() => {
        const progressElement = document.getElementById('post-install-progress');
        if (progressElement) {
            progressElement.remove();
        }
        // Reload stores to show updated status
        loadStores();
    }, 3000);
}

// Load stores from API
async function loadStores() {
    try {
        const response = await fetch('/stores/?include_stats=true', {
            headers: getAuthHeaders()
        });

        if (response.ok) {
            stores = await response.json();
            renderStores();
        } else {
            showError('Failed to load stores');
        }
    } catch (error) {
        showError('Network error while loading stores');
    }
}

// Render stores in the grid
function renderStores() {
    const storesGrid = document.getElementById('stores-grid');
    const emptyState = document.getElementById('empty-state');

    if (stores.length === 0) {
        storesGrid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    storesGrid.style.display = 'grid';
    emptyState.style.display = 'none';

    storesGrid.innerHTML = stores.map(store => createStoreCard(store)).join('');
}

// Create store card HTML
function createStoreCard(store) {
    const stats = store.stats || {};
    const statusClass = `status-${store.status}`;

    return `
        <div class="store-card" data-store-id="${store.id}">
            <div class="store-header">
                <div class="store-name">${store.store_name || store.shop_url}</div>
                <div class="store-url">${store.shop_url}</div>
                <span class="store-status ${statusClass}">${store.status}</span>
            </div>

            <div class="store-stats">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_products || 0}</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_orders || 0}</div>
                        <div class="stat-label">Orders</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${stats.total_customers || 0}</div>
                        <div class="stat-label">Customers</div>
                    </div>
                </div>
            </div>

            <div class="store-actions">
                <button onclick="syncStore(${store.id})" class="btn btn-primary btn-sm">
                    Sync
                </button>
                <button onclick="deleteStore(${store.id})" class="btn btn-danger btn-sm">
                    Delete
                </button>
            </div>
        </div>
    `;
}

// Sync store data
async function syncStore(storeId, forceSync = false) {
    let confirmMessage = 'Sync store data from Shopify?';
    if (forceSync) {
        confirmMessage = 'Force sync store data from Shopify? This will refresh all data even if no changes are detected.';
    }

    if (!confirm(confirmMessage)) return;

    try {
        const requestBody = forceSync ? { force_sync: true } : {};

        const response = await fetch(`/stores/${storeId}/sync`, {
            method: 'POST',
            headers: {
                ...getAuthHeaders(),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message || 'Store sync completed successfully');
            loadStores(); // Reload stores to show updated data
        } else {
            const error = await response.json();
            showError(error.detail || 'Store sync failed');
        }
    } catch (error) {
        showError('Network error during store sync');
    }
}

// Force sync store data
async function forceSyncStore(storeId) {
    await syncStore(storeId, true);
}

// Delete store
async function deleteStore(storeId) {
    const store = stores.find(s => s.id === storeId);
    const storeName = store ? (store.store_name || store.shop_url) : 'this store';

    if (!confirm(`Are you sure you want to delete ${storeName}? This action cannot be undone.`)) {
        return;
    }

    try {
        const response = await fetch(`/stores/${storeId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            showSuccess('Store deleted successfully');
            loadStores(); // Reload stores
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to delete store');
        }
    } catch (error) {
        showError('Network error while deleting store');
    }
}

// Show error message
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';

    // Hide success message
    document.getElementById('success-message').style.display = 'none';

    // Auto-hide after 5 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('success-message');
    successDiv.textContent = message;
    successDiv.style.display = 'block';

    // Hide error message
    document.getElementById('error-message').style.display = 'none';

    // Auto-hide after 3 seconds
    setTimeout(() => {
        successDiv.style.display = 'none';
    }, 3000);
}

// Hide all messages
function hideMessages() {
    document.getElementById('error-message').style.display = 'none';
    document.getElementById('success-message').style.display = 'none';
}

// Make functions available globally for HTML onclick handlers
window.syncStore = syncStore;
window.forceSyncStore = forceSyncStore;
window.deleteStore = deleteStore;
window.showStoreConnectionModal = showStoreConnectionModal;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname === '/stores') {
        initStoresPage();
    }
});
