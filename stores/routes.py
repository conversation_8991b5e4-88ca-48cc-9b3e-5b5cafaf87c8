from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from typing import List, Optional

from database.session import get_db
from database.models import User
from auth.dependencies import get_current_active_user
from .models import (
    StoreUpdate, StoreResponse, StoreWithStats, BulkStoreOperation,
    StoreSyncRequest
)
from .service import StoreService

router = APIRouter(prefix="/stores", tags=["stores"])
store_service = StoreService()

@router.get("/oauth/install")
async def oauth_install(shop: str):
    """
    Step 1: Initiate Shopify OAuth process by redirecting to Shopify authorization page
    """
    import logging
    from fastapi.responses import RedirectResponse
    from .oauth_utils import extract_shop_domain, log_oauth_event

    logger = logging.getLogger(__name__)

    try:
        # Validate and extract shop domain
        validated_shop = extract_shop_domain(shop)
        logger.info(f"Starting OAuth installation for shop: {validated_shop}")

        # Generate OAuth URL
        oauth_url = store_service.generate_oauth_url(validated_shop)

        # Log OAuth event
        log_oauth_event("install_initiated", validated_shop, {"oauth_url": oauth_url})

        logger.info(f"Redirecting shop {validated_shop} to Shopify OAuth: {oauth_url}")
        return RedirectResponse(url=oauth_url)

    except ValueError as e:
        logger.error(f"Invalid shop domain: {shop} - {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid shop domain: {str(e)}")
    except Exception as e:
        logger.error(f"OAuth installation failed for shop {shop}: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="OAuth installation failed")

@router.get("/oauth/callback")
async def oauth_callback(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Shopify OAuth Callback:
    - Validates HMAC
    - Exchanges code for access token
    - Fetches shop data via Shopify GraphQL
    - Saves/updates store info in DB
    - Redirects to frontend with success/error status
    """
    import logging
    from fastapi.responses import RedirectResponse
    from .oauth_utils import verify_hmac, validate_oauth_callback_params, log_oauth_event
    from config import settings

    logger = logging.getLogger(__name__)

    try:
        # Get query parameters
        params = dict(request.query_params)
        logger.info("OAuth callback received")

        # Validate required parameters
        validated_params = validate_oauth_callback_params(params)
        shop = validated_params["shop"]
        code = validated_params["code"]
        hmac_signature = validated_params["hmac"]
        host = validated_params.get("host", "")

        # Verify HMAC for security
        if not verify_hmac(params, hmac_signature):
            logger.error("HMAC verification failed")
            log_oauth_event("callback_hmac_failed", shop)
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="HMAC verification failed")

        logger.info(f"HMAC verified successfully for shop: {shop}")
        log_oauth_event("callback_hmac_verified", shop)

        # Exchange code for permanent access token
        logger.info(f"Exchanging OAuth code for token for shop: {shop}")
        access_token = store_service.exchange_code_for_token(shop, code)

        # Fetch shop data from GraphQL
        logger.info(f"Fetching shop data for shop: {shop}")
        shop_data = store_service.fetch_shop_data_from_graphql(shop, access_token)

        # Save or update store in database
        logger.info(f"Saving store data for shop: {shop}")
        store = store_service.save_or_update_store_from_oauth(db, current_user.id, shop_data, access_token)

        # Log successful OAuth completion
        log_oauth_event("oauth_completed", shop, {
            "store_id": store.id,
            "user_id": current_user.id,
            "store_name": store.store_name
        })

        # Trigger automatic data synchronization and assistant setup
        logger.info(f"Starting automatic data sync and assistant setup for store: {store.id}")
        try:
            # Start background task for data sync and assistant creation
            from .background_tasks import trigger_post_oauth_setup
            trigger_post_oauth_setup(store.id, current_user.id)
            logger.info(f"Background setup task initiated for store: {store.id}")
        except Exception as e:
            logger.warning(f"Failed to start background setup for store {store.id}: {str(e)}")
            # Don't fail the OAuth flow if background task fails

        # Redirect to frontend with success
        redirect_url = f"{settings.ngrok_base_url}?oauth=success&store_id={store.id}&shop={shop}"
        if host:
            redirect_url += f"&host={host}"

        logger.info(f"OAuth flow completed successfully. Redirecting to: {redirect_url}")
        return RedirectResponse(url=redirect_url)

    except ValueError as e:
        logger.error(f"OAuth callback validation error: {str(e)}")
        log_oauth_event("callback_validation_failed", params.get("shop", "unknown"), {"error": str(e)})
        # Redirect to frontend with error
        error_url = f"{settings.ngrok_base_url}?oauth=error&message={str(e)}"
        return RedirectResponse(url=error_url)

    except Exception as e:
        logger.error(f"OAuth callback failed: {str(e)}", exc_info=True)
        log_oauth_event("callback_failed", params.get("shop", "unknown"), {"error": str(e)})
        # Redirect to frontend with error
        error_url = f"{settings.ngrok_base_url}?oauth=error&message=OAuth flow failed"
        return RedirectResponse(url=error_url)

@router.get("/", response_model=List[StoreWithStats])
async def get_user_stores(
    include_stats: bool = Query(False, description="Include store statistics"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all stores for the current user
    """
    stores = store_service.get_user_stores(db, current_user.id, include_stats)
    return [StoreWithStats.model_validate(store) for store in stores]

@router.get("/{store_id}", response_model=StoreWithStats)
async def get_store(
    store_id: int,
    include_stats: bool = Query(True, description="Include store statistics"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific store by ID
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    if include_stats:
        store.stats = store_service.get_store_stats(store)
        # Get assistant info
        from database.crud import AssistantCRUD
        assistant = AssistantCRUD.get_assistant_by_store(db, store.id)
        if assistant:
            store.assistant_status = assistant.status
            store.assistant_name = assistant.assistant_name

    return StoreWithStats.model_validate(store)

@router.put("/{store_id}", response_model=StoreResponse)
async def update_store(
    store_id: int,
    update_data: StoreUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update store information
    """
    store = store_service.update_store(db, store_id, current_user.id, update_data)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    return StoreResponse.model_validate(store)

@router.delete("/{store_id}")
async def delete_store(
    store_id: int,
    permanent: bool = Query(False, description="Permanently delete store"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a store (soft delete by default)
    """
    success = store_service.delete_store(db, store_id, current_user.id, soft_delete=not permanent)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    return {"message": "Store deleted successfully"}

@router.post("/{store_id}/sync")
async def sync_store_data(
    store_id: int,
    sync_request: Optional[StoreSyncRequest] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Sync store data from Shopify
    """
    force_sync = sync_request.force_sync if sync_request else False
    success = store_service.sync_store_data(db, store_id, current_user.id, force=force_sync)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found or sync failed")

    return {"message": "Store data synced successfully"}

@router.post("/bulk-operation")
async def bulk_store_operation(
    operation_data: BulkStoreOperation,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Perform bulk operations on multiple stores
    """
    results = store_service.bulk_operation(
        db, current_user.id, operation_data.store_ids, operation_data.operation
    )
    return results

# Store statistics endpoint
@router.get("/{store_id}/stats")
async def get_store_stats(
    store_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed statistics for a store
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    stats = store_service.get_store_stats(store)
    return stats

# Store health check endpoint
@router.get("/{store_id}/health")
async def check_store_health(
    store_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Check store connection health using OAuth token
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    from database.crud import StoreCRUD
    try:
        access_token = StoreCRUD.decode_token(store.access_token_encrypted)

        # Test connection by fetching shop data
        shop_data = store_service.fetch_shop_data_from_graphql(store.shop_url, access_token)

        if shop_data and shop_data.get('id'):
            # Connection successful
            store_service.update_store(db, store_id, current_user.id, StoreUpdate(status='active'))
            return {
                "store_id": store_id,
                "healthy": True,
                "message": "Store connection is healthy",
                "shop_name": shop_data.get('name'),
                "last_checked": store.last_sync
            }
        else:
            # Connection failed
            store_service.update_store(db, store_id, current_user.id, StoreUpdate(status='error'))
            return {
                "store_id": store_id,
                "healthy": False,
                "message": "Failed to fetch shop data",
                "last_checked": store.last_sync
            }

    except Exception as e:
        store_service.update_store(db, store_id, current_user.id, StoreUpdate(status='error'))
        return {
            "store_id": store_id,
            "healthy": False,
            "message": "Health check failed",
            "error": str(e),
            "last_checked": store.last_sync
        }

# Store setup status endpoint
@router.get("/{store_id}/setup-status")
async def get_store_setup_status(
    store_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get the current setup status for a store (useful for tracking post-OAuth progress)
    """
    store = store_service.get_store_by_id(db, store_id, current_user.id)
    if not store:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Store not found")

    from .background_tasks import get_setup_status
    status_info = get_setup_status(db, store_id)
    return status_info
